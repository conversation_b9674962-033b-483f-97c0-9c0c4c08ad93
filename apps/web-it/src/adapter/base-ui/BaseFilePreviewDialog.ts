import { defineComponent } from 'vue';

import { FilePreviewDialog } from '@vben/base-ui';

import { getPreviewFileExternalLink } from '#/api';

export const BaseFilePreviewDialog = defineComponent({
  ...FilePreviewDialog,
  props: {
    ...(FilePreviewDialog as any).props,
    previewApi: {
      type: Function,
      ...(FilePreviewDialog as any).props.previewApi,
      required: false,
      default: getPreviewFileExternalLink,
    },
  },
});
