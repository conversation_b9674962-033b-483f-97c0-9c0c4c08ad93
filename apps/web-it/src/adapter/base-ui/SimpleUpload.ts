import { defineComponent } from 'vue';

import { BaseUpload } from '@vben/base-ui';

import { getDownloadFileLinkApi, uploadFileApi } from '#/api';

export const SimpleUpload = defineComponent({
  ...BaseUpload,
  props: {
    ...(BaseUpload as any).props,
    uploadApi: {
      type: Function,
      ...(BaseUpload as any).props.uploadApi,
      required: false,
      default: uploadFileApi,
    },
    previewApi: {
      type: Function,
      ...(BaseUpload as any).props.previewApi,
      required: false,
      default: getDownloadFileLinkApi,
    },
  },
});
