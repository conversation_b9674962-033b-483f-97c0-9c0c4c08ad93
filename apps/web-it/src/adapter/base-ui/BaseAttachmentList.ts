import { defineComponent, h } from 'vue';

import { AttachmentList } from '@vben/base-ui';
import { BasicCaption } from '@vben/fe-ui';

import {
  getBusinessFileListApi,
  getDownloadFileLinkApi,
  getFileInfoListApi,
  getPreviewFileExternalLink,
  uploadFileApi,
} from '#/api';
import * as cloudDiskApi from '#/api/core/cloud-disk';

export const BaseAttachmentList = defineComponent({
  props: {
    ...(AttachmentList as any).props,
    tableClassName: {
      type: String,
      default: 'p-4',
    },
    fileInfoApi: {
      type: Function,
      default: getFileInfoListApi,
    },
    previewExternalApi: {
      type: Function,
      default: getPreviewFileExternalLink,
    },
    cloudDiskApiGroup: {
      type: Object,
      default: () => cloudDiskApi,
    },
    downloadApi: {
      type: Function,
      default: getDownloadFileLinkApi,
    },
    uploadApi: {
      type: Function,
      default: uploadFileApi,
    },
    listApi: {
      type: Function,
      default: getBusinessFileListApi,
    },
  },
  emits: ['update:modelValue'],
  setup(props, { attrs, slots, emit }) {
    return () => {
      const childProps = {
        ...props,
        ...attrs,
        'onUpdate:modelValue': (value: any) => emit('update:modelValue', value),
      };

      const childSlots = { ...slots };

      if (!slots.header) {
        childSlots.header = () => [h(BasicCaption, { content: '附件信息' })];
      }

      return h(AttachmentList, childProps, childSlots);
    };
  },
});
