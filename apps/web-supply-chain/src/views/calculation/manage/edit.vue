<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue';

import type { CategoryIndicatorBO, IndicatorListInfo, ScoringIndicatorRuleBO } from '#/api';

import { computed, reactive, ref, watch } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { Alert, InputNumber, message, Select, Table } from 'ant-design-vue';
import BigNumber from 'bignumber.js';

import { getIndicatorConfigListApi } from '#/api';

const emit = defineEmits(['register', 'ok']);
const state = reactive({
  id: 0,
  pageType: 'edit',
});
const pageTitle = computed(() => {
  if (state.pageType === 'detail') {
    return '额度测算详情';
  } else {
    return state.id ? '编辑额度测算' : '新增额度测算';
  }
});
const init = (data: any) => {
  state.id = data.id;
  state.pageType = data.pageType;
  getIndicatorConfig();
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const config = ref<IndicatorListInfo>({});
const tableData = ref<IndicatorListInfo['categoryIndicators']>([]);

// 计算单元格合并的逻辑
const calculateSpan = (dataIndex: string, currentIndex: number) => {
  const data = tableData.value ?? [];
  const currentRow = data?.[currentIndex];

  // 如果当前行没有对应的字段值，不合并
  if (!currentRow || currentRow[dataIndex] === undefined || currentRow[dataIndex] === null) {
    return { rowspan: 1, colspan: 1 };
  }

  const currentValue = currentRow[dataIndex];

  // 检查是否是该值的第一次出现
  let isFirstOccurrence = true;
  for (let i = 0; i < currentIndex; i++) {
    const row = data?.[i];
    if (row && row[dataIndex] === currentValue) {
      isFirstOccurrence = false;
      break;
    }
  }

  // 如果不是第一次出现，返回 rowspan: 0 来隐藏单元格
  if (!isFirstOccurrence) {
    return { rowspan: 0, colspan: 1 };
  }

  // 计算连续相同值的行数
  let rowspan = 1;
  for (let i = currentIndex + 1; i < data.length; i++) {
    const row = data?.[i];
    if (row && row[dataIndex] === currentValue) {
      rowspan++;
    } else {
      break;
    }
  }

  return { rowspan, colspan: 1 };
};

const handleSpan = (_: CategoryIndicatorBO, index: number | undefined, column: any) => {
  const dataIndex = column.dataIndex;

  // 只对 index 和 categoryName 列进行合并
  if (dataIndex === 'index' || dataIndex === 'categoryName') {
    return calculateSpan(dataIndex, index);
  }

  return { rowspan: 1, colspan: 1 };
};

const columns: TableColumnType[] = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 80,
    customCell: (record: any, index: number | undefined) => handleSpan(record, index, { dataIndex: 'index' }),
  },
  {
    title: '指标分类',
    dataIndex: 'categoryName',
    key: 'categoryName',
    width: 200,
    customCell: (record: any, index: number | undefined) => handleSpan(record, index, { dataIndex: 'categoryName' }),
  },
  { title: '指标名称', dataIndex: 'indicatorName', key: 'indicatorName' },
  { title: '数值', dataIndex: 'value', key: 'value', width: 300 },
  { title: '得分', dataIndex: 'score', key: 'score', width: 100 },
  { title: '权重', dataIndex: 'weight', key: 'weight', width: 100 },
  { title: '加权得分', dataIndex: 'weightedScore', key: 'weightedScore', width: 100 },
  { title: '打分标准', dataIndex: 'standard', key: 'standard', width: 400 },
];
const getIndicatorConfig = async (id?: number) => {
  config.value = await getIndicatorConfigListApi({ id });
  const data = config.value.categoryIndicators ?? [];
  const adjustRules = [];
  config.value.adjustRules.forEach((o) => {
    adjustRules.push({
      ruleScore: o.ruleFactor,
      ...o,
    });
  });
  data.push(
    {
      extraType: 'total',
      indicatorName: '总分',
    },
    {
      extraType: 'adjust',
      categoryName: '调整因子',
      indicatorName: '客户分级',
      indicatorRules: adjustRules,
    },
    {
      extraType: 'adjustTotal',
      indicatorName: '调整后总分',
    },
  );
  tableData.value = data;
};
const changeSelectValue = (record: CategoryIndicatorBO, value?: number, option?: ScoringIndicatorRuleBO) => {
  if (record.extraType === 'adjust') {
    record.score = option.ruleFactor;
    record.weightedScore = option.ruleFactor;
  } else {
    record.score = value;
    record.weightedScore = new BigNumber(record.score).times(record.weight).toNumber();
  }
};
const changeRangeValue = (record: CategoryIndicatorBO, value?: number) => {
  // 如果没有值或者没有规则，重置得分
  if (value === undefined || value === null || !record.indicatorRules || record.indicatorRules.length === 0) {
    record.score = 0;
    record.weightedScore = 0;
    return;
  }

  // 根据区间规则计算得分
  let matchedScore = 0;

  for (const rule of record.indicatorRules) {
    const minValue = rule.minValue ? parseFloat(rule.minValue) : null;
    const maxValue = rule.maxValue ? parseFloat(rule.maxValue) : null;

    // 判断值是否落在当前区间内
    let isInRange = false;

    if (minValue !== null && maxValue !== null) {
      // 有上下界的区间，根据规则名称判断是否包含边界
      if (rule.ruleName.startsWith('[')) {
        // 左闭区间 [min, max] 或 [min, max)
        if (rule.ruleName.endsWith(']')) {
          // [min, max] 闭区间
          isInRange = value >= minValue && value <= maxValue;
        } else {
          // [min, max) 左闭右开
          isInRange = value >= minValue && value < maxValue;
        }
      } else if (rule.ruleName.startsWith('(')) {
        // 左开区间 (min, max] 或 (min, max)
        if (rule.ruleName.endsWith(']')) {
          // (min, max] 左开右闭
          isInRange = value > minValue && value <= maxValue;
        } else {
          // (min, max) 开区间
          isInRange = value > minValue && value < maxValue;
        }
      }
    } else if (minValue !== null && maxValue === null) {
      // 只有下界，通常是 (min, +∞) 的形式
      if (rule.ruleName.startsWith('(')) {
        isInRange = value > minValue;
      } else if (rule.ruleName.startsWith('[')) {
        isInRange = value >= minValue;
      }
    } else if (minValue === null && maxValue !== null) {
      // 只有上界，通常是 (-∞, max] 的形式
      if (rule.ruleName.endsWith(']')) {
        isInRange = value <= maxValue;
      } else if (rule.ruleName.endsWith(')')) {
        isInRange = value < maxValue;
      }
    }

    if (isInRange) {
      matchedScore = parseFloat(rule.ruleScore);
      break;
    }
  }

  // 设置得分和加权得分
  record.score = matchedScore;
  record.weightedScore = new BigNumber(record.score).times(record.weight || 0).toNumber();
};
watch(
  () => tableData.value,
  () => {
    const sourceList = tableData.value.filter((o) => !['adjust', 'adjustTotal', 'total'].includes(o.extraType ?? ''));
    let total = new BigNumber(0);
    sourceList.forEach((o) => {
      total = total.plus(o.weightedScore ?? 0);
    });
    tableData.value.find((o) => o.extraType === 'total')!.weightedScore = total.toNumber();
    const adjust = tableData.value.find((o) => o.extraType === 'adjust')!.score;
    tableData.value.find((o) => o.extraType === 'adjustTotal')!.weightedScore = total.times(adjust ?? 1).toNumber();
  },
  { deep: true },
);
const save = () => {
  const sourceList = tableData.value.filter((o) => !['adjust', 'adjustTotal', 'total'].includes(o.extraType ?? ''));
  for (const o of sourceList) {
    if (!o.value) {
      return message.error(`请填写${o.indicatorName}的数值`);
    }
  }
  const adjust = tableData.value.find((o) => o.extraType === 'adjust')!.value;
  if (!adjust) {
    return message.error('请选择客户分级');
  }
  // emit('ok');
  // closePopup();
};
const handleWeight = (record: CategoryIndicatorBO) => {
  return new BigNumber(record.weight).times(100);
};
const setRangeMax = (record: CategoryIndicatorBO) => {
  if (record.signType === 1) {
    return record.maxValue;
  } else if (record.signType === -1) {
    return 0;
  }
};
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    :show-ok-btn="state.pageType !== 'detail'"
    :title="pageTitle"
    @register="registerPopup"
    @ok="save"
  >
    <div class="px-4">
      <Table :data-source="tableData" :columns="columns" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'value'">
            <template v-if="!['adjustTotal', 'total'].includes(record.extraType ?? '')">
              <Select
                v-if="record.inputType === 'SELECT'"
                v-model:value="record.value"
                :options="record.indicatorRules"
                :field-names="{ label: 'ruleName', value: 'ruleScore' }"
                class="w-full max-w-[300px]"
                @change="(value: value, option) => changeSelectValue(record, value, option)"
              />
              <InputNumber
                v-else-if="record.inputType === 'RANGE'"
                v-model:value="record.value"
                :controls="false"
                :min="record.signType === 1 ? 0 : undefined"
                :max="setRangeMax(record)"
                class="w-full max-w-[300px]"
                @change="(value) => changeRangeValue(record, value)"
              />
            </template>
          </template>
          <template v-if="column.key === 'weight'">
            <span v-if="!record.extraType">{{ handleWeight(record) }}%</span>
          </template>
          <template v-if="column.key === 'standard'">
            <p v-for="(item, index) in record.indicatorRules" :key="index">
              <span v-if="record.extraType === 'adjust'">{{ item.ruleName }}：总得分 * {{ item.ruleScore }}</span>
              <span v-else>{{ item.ruleName }}：{{ Number(item.ruleScore) }}</span>
            </p>
          </template>
        </template>
      </Table>
      <Alert type="info" class="my-4">
        <template #message>
          <div class="whitespace-pre-wrap">
            {{ config.description }}
          </div>
        </template>
      </Alert>
    </div>
  </BasicPopup>
</template>

<style></style>
