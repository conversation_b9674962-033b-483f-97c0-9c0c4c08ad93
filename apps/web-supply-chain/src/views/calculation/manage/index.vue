<script setup lang="ts">
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';

import CalculationEdit from './edit.vue';

const [registerPage, { openPopup: openFormPopup }] = usePopup();
const editSuccess = () => {
  console.log('edit success');
};
</script>

<template>
  <Page auto-content-height>
    <a-button @click="openFormPopup(true, {})">新增</a-button>
    <CalculationEdit @register="registerPage" @ok="editSuccess" />
  </Page>
</template>

<style></style>
